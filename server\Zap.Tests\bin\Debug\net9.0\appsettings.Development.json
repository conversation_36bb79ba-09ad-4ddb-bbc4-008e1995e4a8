{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=Zap_Dev;Username=postgres;Password=password"}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning", "Zap.Api.Features.Authentication": "Debug", "Zap.Api.Common.Authorization": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}], "Enrich": ["FromLogContext"], "Properties": {"ApplicationName": "Zap.Api"}}}